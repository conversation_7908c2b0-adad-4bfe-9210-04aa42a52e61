<template>
  <view class="page">
    <!-- 头部背景 -->
    <view class="head-bg">
      <view class="wallet-balance">
        <view class="wallet">
          <text>
            <text class="iconfont icon-user" style="margin-right: 20rpx;"></text>
            团队人数({{ total }})
          </text>
        </view>
      </view>

      <view class="bg">
        <image src="/static/integral_bg1.png" mode=""></image>
      </view>
    </view>
    <scroll-view class="record-list" @scrolltolower="lower" style="height: calc(100vh - 180rpx);" scroll-y>
      <view class="list s-flex-bt" v-for="(item,index) in amountList" :key="index" v-if="amountList && amountList.length > 0">
        <view class="title-date">
          <view class="title">
            <view style="width: 46vw;">{{ item.nickname || item.nickname }}
              <text
                  style="font-size: 80%;color: gray;margin-left: 20rpx;font-weight: 100;">等级:{{ item.levelStr }}
                {{ item.agent ? ' 代理:' + item.agent : '' }}
              </text>
            </view>
          </view>
          <view class="date">
            <text>注册：{{ item.addtime }}</text>
          </view>
        </view>
        <view class="title-date">
          <view class="title title2">
            <!-- <text>分享：{{item.pathNum}}</text> -->
            <text @click="call(item.mobile)" class="iconfont icon-kefu"
                  style="background-color: rgba(255, 0, 0, 0.1);padding: 0rpx 20rpx;border-radius: 40rpx;line-height: 40rpx;color: #fe3b0f;">
              一键拨号
            </text>
          </view>
          <view class="date title2">
            <text v-if="item.lastAuctionDate == '1970-01-01'">最近参拍：无</text>
            <text v-else>最近参拍：{{ item.lastAuctionDate }}</text>
          </view>
        </view>
      </view>
      <view v-if="amountList.length === 0 && !isLoading" class="on-anything">
        暂无团队成员数据
      </view>
      <view v-show="noMore && amountList.length > 0" class="on-anything">
        ———— 没有更多了 ————
      </view>
      <view v-if="isLoading" class="loading">
        <view class="loading-icon"></view>
        <text>加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      page: 1,
      amountList: [],
      amount: 0,
      cint: 0, //0所有日志，1审核中，2已提现，3订单日志
      goto: true,
      noMore: false,
      selectIndex: 0,
      isLoading: false,
      limit: 10,
      total: 0
    };
  },
  onLoad(params) {
    this.amount = params.amount || 0
    this.cint = params.cint || 0
    this.$login.checkLogin({
      login: true
    })
    this.selectIndex = params.cid ? params.cid : 0
    this.getUserAmount(this.selectIndex)
  },

  // 支持下拉刷新
  onPullDownRefresh() {
    this.getUserAmount(this.selectIndex)
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },
  methods: {
    call($mobile = '') {
      // #ifdef H5
      // H5 环境
      window.location.href = 'tel:' + $mobile;
      // #endif

      // #ifdef APP-PLUS
      // App 环境
      uni.makePhoneCall({
        phoneNumber: $mobile,
        success: function () {
          console.log('success');
        },
        fail: function () {
          console.log('fail');
        }
      });
      // #endif
    },
    getUserAmount(cint) {
      this.selectIndex = cint
      this.page = 1
      this.amountList = []
      this.noMore = false
      this.getUserAmountList()
    },
    getUserAmountList() {
      if (this.isLoading || this.noMore) return

      this.isLoading = true
      var that = this
      that.$http.post('getMyTeam', {
        token: getApp().globalData.token,
        page: that.page,
        limit: that.limit // 每页N条数据，便于分页加载
      }).then(res => {
        that.isLoading = false
        that.goto = true // 重置goto状态，允许再次触发加载

        if (res.code == 0) {
          this.total = res.count
          if (that.page === 1) {
            that.amountList = res.data || []
          } else {
            that.amountList = [...that.amountList, ...(res.data || [])]
          }

          // 如果返回的数据少于请求的数量，说明没有更多数据了
          that.noMore = !res.data || res.data.length < that.limit
          that.amount = res.msg
        } else {
          that.noMore = true
          uni.showToast({
            title: res.msg || '加载失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        that.isLoading = false
        that.goto = true
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        })
      })
    },
    lower() {
      if (!this.noMore && !this.isLoading) {
        this.page++
        this.getUserAmountList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import 'Team.scss';
</style>
