<template>
	<view class="page">
		<!-- 头部背景 -->
		<view class="head-bg">
			<view class="bg">
				<image src="/static/integral_bg1.png" mode=""></image>
			</view>
		</view>
		<!-- 记录列表 -->
<!-- 		<view v-if="amountList.length<1" class="on-anything">
			———— 没有任何数据 ————
		</view> -->
		<scroll-view class="record-list" @scrolltolower="lower" style="height: calc(100vh - 120rpx);" scroll-y>
			<view class="list s-flex-bt" v-for="(item,index) in amountList" :key="index">
				<view style="width: 20%;">
					<image :src="api + item.img" style="width: 15vw;height: 15vw;" mode="aspectFill"></image>
				</view>
				<view class="title-date s-flex-column s-flex-justcon-center" style="padding-right: 20rpx;">
					<view class="title">
						<text class="one-omit" style="font-weight: normal;">{{item.goodsTitle}}</text>
					</view>
					<view class="date">
						<text>{{item.addDate}}</text>
					</view>
				</view>
				<view class="title-date s-flex-column s-flex-justcon-center" style="width: 28%;">
					<view class="title title2">
						<text v-if="selectIndex==3">收益：{{item.income2}}</text>
						<text v-else>{{item.amount}}</text>
					</view>
					<view class="date title2">
						<text v-if="selectIndex==1">参拍数：{{item.numbers}}</text>
						<text v-else>委拍数：{{item.sellNumber}}</text>
					</view>
				</view>
			</view>
			<view v-show="noMore" class="on-anything">
				———— 没有更多了 ————
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page:1,
				amountList:{},
				amount:0,
				cint:0,//0所有日志，1审核中，2已提现，3订单日志
				goto:true,
				noMore:false,
				api:getApp().globalData.apiUrl,
				selectIndex:0,
				titleArr:['','参拍日志','委拍日志','拍卖收益']
			};
		},
		onLoad(params) {
			this.amount = params.amount
			this.cint = params.cint
			this.$login.checkLogin({login:true})
			this.selectIndex = (params.cid===0 || params.cid>0)?params.cid:0
			if(params.cid===0 || params.cid>0){
				console.log(this.selectIndex)
				this.page=1
				this.amountList={}
			}
			this.getUserAmount(this.selectIndex)
			// this.getUserAmount()
			this.setTitle()
		},
		methods:{
			setTitle(){
				uni.setNavigationBarTitle({
					title:this.titleArr[this.selectIndex]
				})
			},
			getUserAmount(cint){
				this.setTitle()
				this.selectIndex = cint
				this.page = 1
				this.amountList={}
				this.getUserAmountList()
			},
			getUserAmountList(cint){
				var that = this
				that.$http.post('getUserAuctionLog', {
					token: getApp().globalData.token,
					selectIndex:that.selectIndex * 1 + 1,
					page:that.page,
				}).then(res => {
					if (res.code == 0) {
						that.amountList = {...res.data,...that.amountList}
						if(res.data.length<10) that.noMore = true
					} else {
						if(res.data.length<10) that.noMore = true
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			lower(){
				if(this.goto){
					this.page++
					this.getUserAmountList()
				}
				this.goto=false
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'AuctionLog.scss';
</style>
